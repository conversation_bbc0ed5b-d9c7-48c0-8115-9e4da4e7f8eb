import { supabase } from "../supabase";
import type { PaginatedResponse, ActivityEvent } from "../../types/admin";

/**
 * Format activity details for human-readable display
 */
export function formatActivityDetails(
  actionType: string,
  details: Record<string, any> | null
): string {
  if (!details) return "-";

  // Handle different action types with specific formatting
  switch (actionType) {
    case "login":
      return "User logged in";

    case "logout":
      return "User logged out";

    case "upload":
      return details.document_name
        ? `Uploaded document: ${details.document_name}`
        : "Uploaded a document";

    case "share":
      if (details.document_name && details.shared_with_email) {
        return `Shared "${details.document_name}" with ${details.shared_with_email}`;
      } else if (details.document_name) {
        return `Shared document: ${details.document_name}`;
      }
      return "Shared a document";

    case "join_room":
      return details.room_name
        ? `Joined room: ${details.room_name}`
        : "Joined a room";

    case "create_room":
      return details.room_name
        ? `Created room: ${details.room_name}`
        : "Created a room";

    case "delete_document":
      return details.document_name
        ? `Deleted document: ${details.document_name}`
        : "Deleted a document";

    case "user_activate":
      return details.target_user_email
        ? `Activated user: ${details.target_user_email}`
        : "Activated a user account";

    case "user_deactivate":
      const reason =
        details.reason && details.reason !== "No reason provided"
          ? ` (Reason: ${details.reason})`
          : "";
      return details.target_user_email
        ? `Deactivated user: ${details.target_user_email}${reason}`
        : `Deactivated a user account${reason}`;

    case "user_delete":
      return details.user_email
        ? `Deleted user account: ${details.user_email}`
        : "Deleted a user account";

    case "admin_action":
      return details.description || "Performed admin action";

    default:
      // Fallback: try to extract meaningful information
      if (details.description) {
        return details.description;
      }
      if (details.document_name) {
        return `Action on document: ${details.document_name}`;
      }
      if (details.room_name) {
        return `Action on room: ${details.room_name}`;
      }
      if (details.target_user_email) {
        return `Action on user: ${details.target_user_email}`;
      }

      // Last resort: show action type in readable format
      return actionType
        .replace(/_/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase());
  }
}

/**
 * Verify admin access before performing operations
 */
async function verifyAdminAccess(): Promise<boolean> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return false;

    // Check metadata first
    if (user.user_metadata?.role === "admin") return true;

    // Fallback to profile check
    const { data: profile } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single();

    return profile?.role === "admin";
  } catch {
    return false;
  }
}

/**
 * Log user activity
 */
export async function logUserActivity(
  userId: string,
  actionType: string,
  targetType?: string,
  targetId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    const { error } = await supabase.rpc("log_user_activity", {
      p_user_id: userId,
      p_action_type: actionType,
      p_target_type: targetType || null,
      p_target_id: targetId || null,
      p_details: details || null,
      p_ip_address: ipAddress || null,
      p_user_agent: userAgent || null,
    });

    if (error) {
      console.error("Failed to log user activity:", error);
    }
  } catch (error) {
    console.error("Failed to log user activity:", error);
  }
}

/**
 * Get all activity logs with filtering and pagination
 */
export async function getAllActivity(
  page: number = 1,
  limit: number = 50,
  filters?: {
    user_id?: string;
    action_type?: string;
    target_type?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
  }
): Promise<PaginatedResponse<ActivityEvent>> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    // Join with profiles table to get user information
    let query = supabase.from("activity_logs").select(
      `
        *,
        profiles!activity_logs_user_id_fkey(full_name, email)
      `,
      { count: "exact" }
    );

    // Apply filters
    if (filters?.user_id) {
      query = query.eq("user_id", filters.user_id);
    }
    if (filters?.action_type) {
      query = query.eq("action_type", filters.action_type);
    }
    if (filters?.target_type) {
      query = query.eq("target_type", filters.target_type);
    }
    if (filters?.date_from) {
      query = query.gte("created_at", filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte("created_at", filters.date_to);
    }
    if (filters?.search) {
      // Search in action_type or details
      query = query.or(
        `action_type.ilike.%${filters.search}%,details->>description.ilike.%${filters.search}%`
      );
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    // Transform data to match ActivityEvent interface
    const activities: ActivityEvent[] = (data || []).map((activity: any) => ({
      id: activity.id,
      user_id: activity.user_id,
      user_name: activity.profiles?.full_name || null,
      user_email: activity.profiles?.email || "Unknown",
      action_type: activity.action_type,
      target_type: activity.target_type,
      target_id: activity.target_id,
      details: activity.details,
      created_at: activity.created_at,
    }));

    return {
      data: activities,
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
  } catch (error: any) {
    throw new Error(`Failed to get activity logs: ${error.message}`);
  }
}

/**
 * Get user-specific activity logs
 */
export async function getUserActivityLogs(
  userId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<ActivityEvent>> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabase
      .from("activity_logs")
      .select("*", { count: "exact" })
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    // Get user info separately
    let userInfo = null;
    if (data && data.length > 0) {
      const { data: profile } = await supabase
        .from("profiles")
        .select("full_name, email")
        .eq("id", userId)
        .single();
      userInfo = profile;
    }

    // Transform data
    const activities: ActivityEvent[] = (data || []).map((activity: any) => ({
      id: activity.id,
      user_id: activity.user_id,
      user_name: userInfo?.full_name || null,
      user_email: userInfo?.email || "Unknown",
      action_type: activity.action_type,
      target_type: activity.target_type,
      target_id: activity.target_id,
      details: activity.details,
      created_at: activity.created_at,
    }));

    return {
      data: activities,
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
  } catch (error: any) {
    throw new Error(`Failed to get user activity: ${error.message}`);
  }
}

/**
 * Get activity statistics
 */
export async function getActivityStats(): Promise<{
  total_activities: number;
  activities_today: number;
  activities_this_week: number;
  top_actions: Array<{ action_type: string; count: number }>;
  active_users_today: number;
}> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const { data, error } = await supabase.rpc("get_activity_statistics");

    if (error) throw error;

    return data;
  } catch (error: any) {
    throw new Error(`Failed to get activity stats: ${error.message}`);
  }
}

/**
 * Delete old activity logs (cleanup function)
 */
export async function cleanupOldActivities(
  daysToKeep: number = 90
): Promise<number> {
  if (!(await verifyAdminAccess())) {
    throw new Error("Admin access required");
  }

  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { data, error } = await supabase
      .from("activity_logs")
      .delete()
      .lt("created_at", cutoffDate.toISOString());

    if (error) throw error;

    return Array.isArray(data) ? (data as any[]).length : 0;
  } catch (error: any) {
    throw new Error(`Failed to cleanup old activities: ${error.message}`);
  }
}

// Export the activity service
export const activityService = {
  logUserActivity,
  getAllActivity,
  getUserActivityLogs,
  getActivityStats,
  cleanupOldActivities,
};
